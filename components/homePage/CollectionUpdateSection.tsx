"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Icon } from "@/components/common/Icon"

export function CollectionUpdateSection() {
  const handleShopNow = () => {
    console.log("Shop Now clicked")
    // Add navigation logic here
  }

  return (
    <section className=" bg-primary-50 dark:bg-primary-900/20 relative overflow-hidden">
      {/* Sun Icon - Top Left Corner */}
      <div className="absolute top-0 left-0 z-10">
        <Icon name="sun-icon" size={32} className="text-primary-600 dark:text-primary-400" />
      </div>

      <div className=" grid-cols-1 lg:grid-cols-2 flex justify-between gap-12 lg:gap-16 items-end">
        {/* Left Content */}
        <div className="space-y-8 justify-end flex flex-col items-center mx-auto mb-14  w-full  ">
          <div>
            {/* Main Heading */}
            <h2 className="text-4x max-w-2xl lg:text-5xl uppercase font-bold text-primary-600 dark:text-white leading-tight">
              Let's keep the collection of jewelry up to date
            </h2>

            {/* Shop Now Button */}
            <div className="pt-4 self-start">
              <Button
                onClick={handleShopNow}
                className="bg-primary-600 hover:bg-primary-700 dark:bg-primary-500 dark:hover:bg-primary-600
              text-white px-8 py-4 text-lg font-semibold rounded-lg
              transition-all duration-300 hover:scale-105 hover:shadow-lg
              flex items-center gap-3 group"
              >
                Shop Now
                <Icon
                  name="arrow-right"
                  size={20}
                  className="transition-transform duration-300 group-hover:translate-x-1"
                />
              </Button>
            </div>
          </div>
        </div>

        <div className="max-w-[479px] max-h-[403px] ">
          <img
            src="/images/collection-update/jewelry-showcase.jpg"
            alt="Latest jewelry collection showcase"
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.style.display = "none"
            }}
          />
        </div>
      </div>
    </section>
  )
}
