"use client"

import { useTranslation } from "react-i18next"
import Image from "next/image"
import Icon from "../common/Icon"

export function DiscoverSection() {
  const { t } = useTranslation()

  return (
    <section className="py-20 bg-primary-50  dark:bg-secondary-600   relative ">
      <div className="absolute -top-4 -right-4 z-10">
        <div className="w-16 h-16 flex items-center justify-center ">
          <Icon name="sun-icon" size={20} className="text-white" />
        </div>
      </div>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className=" container border-2 border-t border-primary-600 w-full absolute top-0" />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div className="space-y-8 lg:pr-8">
            <div className="space-y-2">
              <h2 className="text-3xl md:text-4xl lg:text-6xl font-light text-gray-900 dark:text-white leading-tight">
                {t("discover.title")}
                <span className="text-primary-500 font-normal">
                  {t("discover.highlight")}
                </span>
              </h2>
              <h3 className="text-3xl md:text-4xl lg:text-6xl font-light text-gray-900 dark:text-white leading-tight">
                <span className="text-primary-500 font-normal">
                  {t("discover.jewelry")}
                </span>{" "}
                {t("discover.subtitle")}
              </h3>
            </div>

            <div className="space-y-8 mt-10">
              <p className="text-gray-950 dark:text-gray-300 text-lg leading-relaxed max-w-md">
                {t("discover.company_story")}
              </p>

              <p className="text-gray-950 dark:text-gray-300 text-lg leading-relaxed max-w-md">
                {t("discover.founder_story")}
              </p>
            </div>
            <div>
              <Icon name="Asset" size={128} />
            </div>
          </div>

          {/* Right Content - Image with border and stars */}
          <div className="relative p-8 border border-1 border-gray-500">
            {/* Top-right icon */}

            <div className="absolute left-[-37px] z-2 top-1/2 -translate-y-1/2 flex flex-col items-center gap-2">
              <div className="absolute -top-6 -left-6 ">
                <Icon name="star4" size={28} />
              </div>
              <div className="absolute top-20 -left-1 ">
                <Icon name="star3" size={24} className="absolute top-1/3" />
              </div>
              <div className="z-4 relative">
                <Icon name="star2" size={20} />
              </div>
            </div>
            <div className="relative  overflow-hidden aspect-[3/4]">
              {/* Decorative stars */}

              {/* Image */}
              <Image
                src="/images/discover/jewelry-model.png"
                alt="Elegant jewelry model"
                fill
                className="object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.style.display = "none"
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
