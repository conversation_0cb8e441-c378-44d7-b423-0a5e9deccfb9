"use client"

import { useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { BlogCard } from "@/components/common/BlogCard"
import { cn } from "@/lib/utils"

export function BlogSection() {
  const { t } = useTranslation()
  const sectionRef = useRef<HTMLElement>(null)
  const cardsContainerRef = useRef<HTMLDivElement>(null)
  const [scrollProgress, setScrollProgress] = useState(0)

  // Sample blog data - replace with your actual blog posts
  const blogPosts = [
    {
      id: 1,
      title: "The Art of Jewelry Craftsmanship: Traditional Techniques in Modern Times",
      excerpt: "Discover how ancient jewelry-making techniques are being preserved and adapted for contemporary designs, creating timeless pieces that honor tradition.",
      date: "Dec 15, 2024",
      category: "Craftsmanship",
      image: "/images/blog/jewelry-craft.jpg",
      readTime: "6 min read"
    },
    {
      id: 2,
      title: "Gold Market Trends: What Investors Need to Know in 2024",
      excerpt: "An in-depth analysis of current gold market conditions, price predictions, and strategic investment opportunities for the coming year.",
      date: "Dec 12, 2024",
      category: "Market Insights",
      image: "/images/blog/gold-market.jpg",
      readTime: "8 min read"
    },
    {
      id: 3,
      title: "Sustainable Jewelry: The Future of Ethical Luxury",
      excerpt: "Exploring the growing trend of sustainable and ethically sourced jewelry, and how it's reshaping the luxury market landscape.",
      date: "Dec 10, 2024",
      category: "Sustainability",
      image: "/images/blog/sustainable-jewelry.jpg",
      readTime: "5 min read"
    },
    {
      id: 4,
      title: "Precious Metals Trading: A Beginner's Complete Guide",
      excerpt: "Everything you need to know about trading precious metals, from basic concepts to advanced strategies for successful investments.",
      date: "Dec 8, 2024",
      category: "Trading",
      image: "/images/blog/metals-trading.jpg",
      readTime: "10 min read"
    },
    {
      id: 5,
      title: "The History and Significance of Diamond Cuts",
      excerpt: "Journey through the evolution of diamond cutting techniques and understand how different cuts affect brilliance, fire, and overall beauty.",
      date: "Dec 5, 2024",
      category: "Education",
      image: "/images/blog/diamond-cuts.jpg",
      readTime: "7 min read"
    }
  ]

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return

      const section = sectionRef.current
      const rect = section.getBoundingClientRect()
      const windowHeight = window.innerHeight
      
      // Calculate when section enters and exits viewport
      const sectionTop = rect.top
      const sectionBottom = rect.bottom
      const sectionHeight = rect.height
      
      // Start animation when section is 50% visible
      const startOffset = windowHeight * 0.5
      const endOffset = windowHeight * 0.1
      
      if (sectionTop <= startOffset && sectionBottom >= endOffset) {
        // Section is in view, calculate progress
        const visibleHeight = Math.min(windowHeight - sectionTop, sectionHeight)
        const maxVisibleHeight = sectionHeight + startOffset - endOffset
        const progress = Math.max(0, Math.min(1, visibleHeight / maxVisibleHeight))
        
        setScrollProgress(progress)
      } else if (sectionTop > startOffset) {
        // Section hasn't entered yet
        setScrollProgress(0)
      } else {
        // Section has passed
        setScrollProgress(1)
      }
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll() // Initial call

    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleBlogClick = (blogId: number) => {
    console.log(`Clicked on blog post ${blogId}`)
    // Add navigation logic here
  }

  // Calculate transform based on scroll progress
  const maxTranslateX = 300 // Maximum pixels to move right
  const translateX = scrollProgress * maxTranslateX

  return (
    <section 
      ref={sectionRef}
      className="py-20 bg-gray-50 dark:bg-secondary-700 overflow-hidden"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <p className="text-primary-500 text-lg uppercase tracking-wider mb-2">
            Latest Insights
          </p>
          <h2 className="text-3xl md:text-6xl font-bold text-secondary-500 dark:text-white mb-4">
            Our Blog
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Stay updated with the latest trends, insights, and expert knowledge in jewelry, precious metals, and market analysis.
          </p>
        </div>

        {/* Blog Cards with Scroll Animation */}
        <div className="relative">
          <div 
            ref={cardsContainerRef}
            className="flex gap-8 transition-transform duration-300 ease-out"
            style={{
              transform: `translateX(${translateX}px)`
            }}
          >
            {blogPosts.map((post) => (
              <BlogCard
                key={post.id}
                title={post.title}
                excerpt={post.excerpt}
                date={post.date}
                category={post.category}
                image={post.image}
                readTime={post.readTime}
                onClick={() => handleBlogClick(post.id)}
                className="animate-fade-in"
              />
            ))}
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
            <span>Scroll to explore</span>
            <div className="w-16 h-1 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
              <div 
                className="h-full bg-primary-500 transition-all duration-300 ease-out"
                style={{ width: `${scrollProgress * 100}%` }}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
