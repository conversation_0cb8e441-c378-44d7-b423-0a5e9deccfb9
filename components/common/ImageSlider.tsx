"use client"

import React, { useCallback, useEffect, useState } from 'react'
import useEmblaCarousel from 'embla-carousel-react'
import Autoplay from 'embla-carousel-autoplay'
import { Icon } from '@/components/common/Icon'
import { cn } from '@/lib/utils'

interface ImageSliderProps {
  images: {
    src: string
    alt: string
  }[]
  autoplay?: boolean
  autoplayDelay?: number
  className?: string
}

export function ImageSlider({
  images,
  autoplay = true,
  autoplayDelay = 4000,
  className
}: ImageSliderProps) {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([])

  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'center',
      skipSnaps: false,
      dragFree: false
    },
    autoplay ? [Autoplay({ delay: autoplayDelay, stopOnInteraction: false })] : []
  )

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev()
  }, [emblaApi])

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext()
  }, [emblaApi])

  const scrollTo = useCallback(
    (index: number) => {
      if (emblaApi) emblaApi.scrollTo(index)
    },
    [emblaApi]
  )

  const onInit = useCallback((emblaApi: any) => {
    setScrollSnaps(emblaApi.scrollSnapList())
  }, [])

  const onSelect = useCallback((emblaApi: any) => {
    setSelectedIndex(emblaApi.selectedScrollSnap())
  }, [])

  useEffect(() => {
    if (!emblaApi) return

    onInit(emblaApi)
    onSelect(emblaApi)
    emblaApi.on('reInit', onInit)
    emblaApi.on('select', onSelect)
  }, [emblaApi, onInit, onSelect])

  return (
    <div className={cn("relative w-full", className)}>
      {/* Main Slider */}
      <div className="overflow-hidden" ref={emblaRef}>
        <div className="flex">
          {images.map((image, index) => (
            <div key={index} className="flex-[0_0_100%] min-w-0">
              <div className="relative w-full h-[60vh] md:h-[70vh] lg:h-[80vh]">
                <img
                  src={image.src}
                  alt={image.alt}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement
                    target.style.display = "none"
                  }}
                />
                {/* Dark overlay for better contrast */}
                <div className="absolute inset-0 bg-black/20" />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Arrows */}
      <button
        type="button"
        className="absolute left-4 md:left-8 top-1/2 -translate-y-1/2 z-10
                   bg-white/10 hover:bg-white/20 backdrop-blur-sm
                   border border-white/20 rounded-full p-3 md:p-4
                   transition-all duration-300 hover:scale-110
                   text-white hover:text-white"
        onClick={scrollPrev}
        aria-label="Previous image"
      >
        <Icon name="arrow-left" size={20} />
      </button>

      <button
        type="button"
        className="absolute right-4 md:right-8 top-1/2 -translate-y-1/2 z-10
                   bg-white/10 hover:bg-white/20 backdrop-blur-sm
                   border border-white/20 rounded-full p-3 md:p-4
                   transition-all duration-300 hover:scale-110
                   text-white hover:text-white"
        onClick={scrollNext}
        aria-label="Next image"
      >
        <Icon name="arrow-right" size={20} />
      </button>

      {/* Slide Counter - Bottom Right */}
      <div className="absolute bottom-4 md:bottom-8 right-4 md:right-8 z-10">
        <div className="bg-black/40 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/20">
          <span className="text-white font-medium text-sm md:text-base">
            {String(selectedIndex + 1).padStart(2, '0')} / {String(images.length).padStart(2, '0')}
          </span>
        </div>
      </div>

      {/* Dot Indicators (Optional - can be hidden) */}
      {/* <div className="absolute bottom-4 md:bottom-8 left-1/2 -translate-x-1/2 z-10 hidden md:flex gap-2">
        {scrollSnaps.map((_, index) => (
          <button
            key={index}
            type="button"
            className={cn(
              "w-2 h-2 rounded-full transition-all duration-300",
              index === selectedIndex
                ? "bg-white scale-125"
                : "bg-white/40 hover:bg-white/60"
            )}
            onClick={() => scrollTo(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div> */}
    </div>
  )
}
