"use client"

import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface BlogCardProps {
  title: string
  excerpt: string
  date: string
  category: string
  image?: string
  readTime?: string
  className?: string
  onClick?: () => void
}

export function BlogCard({
  title,
  excerpt,
  date,
  category,
  image,
  readTime = "5 min read",
  className,
  onClick
}: BlogCardProps) {
  return (
    <Card
      className={cn(
        "group cursor-pointer transition-all duration-300 hover:-translate-y-2",
        "overflow-hidden bg-white dark:bg-secondary-600 border border-gray-200 dark:border-secondary-700",
        "shadow-lg hover:shadow-xl",
        "min-w-[320px] max-w-[380px] flex-shrink-0",
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-0">
        {/* Image Container */}
        <div className="relative overflow-hidden h-48">
          {image ? (
            <img
              src={image}
              alt={title}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            // Placeholder with gradient background
            <div className="w-full h-full bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-800 dark:to-primary-900 flex items-center justify-center">
              <div className="text-4xl text-primary-400 dark:text-primary-300">
                📝
              </div>
            </div>
          )}
          
          {/* Category Badge */}
          <div className="absolute top-4 left-4">
            <span className="bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium uppercase tracking-wider">
              {category}
            </span>
          </div>

          {/* Hover Overlay */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Meta Information */}
          <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-3">
            <span>{date}</span>
            <span>{readTime}</span>
          </div>

          {/* Title */}
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
            {title}
          </h3>

          {/* Excerpt */}
          <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed line-clamp-3">
            {excerpt}
          </p>

          {/* Read More Link */}
          <div className="mt-4 pt-4 border-t border-gray-100 dark:border-secondary-700">
            <span className="text-primary-600 dark:text-primary-400 text-sm font-medium group-hover:text-primary-700 dark:group-hover:text-primary-300 transition-colors duration-300">
              Read More →
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
