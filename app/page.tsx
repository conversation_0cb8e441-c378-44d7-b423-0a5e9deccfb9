"use client"

import { MainLayout } from "@/components/layout/MainLayout"
import Banner from "@/components/homePage/banner"
import { WhatWeOffer } from "@/components/homePage/WhatWeOffer"
import { PromoBanner } from "@/components/homePage/PromoBanner"
import { AttractiveJewellery } from "@/components/homePage/AttractiveJewellery"
import { DiscoverSection } from "@/components/homePage/DiscoverSection"
import { CursorSection } from "@/components/homePage/CursorSection"
import { CollectionUpdateSection } from "@/components/homePage/CollectionUpdateSection"
export default function Home() {
  return (
    <MainLayout hasFullScreenBanner={true}>
      {/* Full-screen video banner */}
      <Banner />

      {/* What We Offer Services Section */}
      <WhatWeOffer />

      {/* Promotional Banner with Moving Text */}
      <PromoBanner speed="slow" />

      {/* Attractive Jewellery Collections Section */}
      <AttractiveJewellery />

      {/* Discover the Best Jewelry Section */}
      <DiscoverSection />

      {/* Cursor Section - Full Width Image Slider */}
      <CursorSection />

      {/* Collection Update Section - Call to Action */}
      <CollectionUpdateSection />
    </MainLayout>
  )
}
